from django.db import models
from django.contrib.auth.models import AbstractUser
import re
from django.core.exceptions import ValidationError

def validate_library_id(value):
    if not re.match(r'^[A-Z][a-zA-Z]+[0-9]{4}$', value):
        raise ValidationError("L’identifiant doit être unique et respecter le format suivant : une majuscule au début, suivie de lettres, et se terminant par 4 chiffres.")

def emailValidator(value):
    if not value.endswith('@esprit.tn'):
        raise ValidationError('Invalid email address. Only @esprit.tn addresses are allowed')
#abstarct user n7otouha fi hedy w naamlou primary key fi id
class Client(AbstractUser):
    library_identifier = models.CharField( max_length=100,primary_key=True, unique=True, validators=[validate_library_id] )
    email = models.EmailField(max_length=250, unique=True, validators=[emailValidator])
    created_at = models.DateTime<PERSON><PERSON>(null=True)
    updated_at = models.DateTimeField(null=True)
