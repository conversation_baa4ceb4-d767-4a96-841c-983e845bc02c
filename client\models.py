from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
# Create your models here.
#email : Doit appartenir au domaine @esprit.tn
def emailValidator(value):
    if not value.endswith('@esprit.tn'):
        raise ValidationError('Invalid email address. Only @esprit.tn addresses are allowed')
class Client(AbstractUser):
    #. cin : Vérifier qu’il contient exactement 8 chiffres
    digitsOnly = RegexValidator(r'^\d{8}$', 'This field must contain exactly 8 digits')
    cin = models.Char<PERSON><PERSON>(primary_key=True, max_length=8, validators=[digitsOnly])
#email : Doit appartenir au domaine @esprit.tn
    email = models.EmailField(max_length=250, unique=True, validators=[emailValidator])
    first_name= models.Char<PERSON><PERSON>(max_length=200)
    last_name= models.Char<PERSON><PERSON>(max_length=200)
    username = models.Char<PERSON><PERSON>(unique=True, max_length=150)
    
   
    
USERNAME_FIELD = 'username'
    
    