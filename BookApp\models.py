from django.db import models
from django.core.exceptions import ValidationError
from categoryApp.models import Category
from client.models import Client

class Book(models.Model):
    title = models.CharField(max_length=200)
    author = models.CharField(max_length=100)
    publication_date = models.DateField()
    book_file = models.FileField(upload_to='books/', null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    created_at = models.DateTimeField(null=True)
    updated_at = models.DateTimeField(null=True)

    def __str__(self):
        return self.title
# hnee mt3 client w mt3 book houma des cles etrangères fi borrowing
class Borrowing(models.Model):
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='borrowings')
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    borrowing_date = models.DateField()
    return_date = models.DateField()
    returned = models.BooleanField(default=False)
    created_at = models.DateTimeField(null=True)
    updated_at = models.DateTimeField(null=True)

    def clean(self):
        if self.return_date < self.borrowing_date:
            raise ValidationError("La date de retour doit être postérieure ou égale à la date d'emprunt.")
